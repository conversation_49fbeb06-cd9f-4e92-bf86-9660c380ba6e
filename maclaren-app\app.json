{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "maclaren-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#007AFF"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "Esta aplicación necesita acceso a tu ubicación para mostrar motoristas cercanos y calcular rutas.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Esta aplicación necesita acceso a tu ubicación para mostrar motoristas cercanos y calcular rutas."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#007AFF"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "FOREGROUND_SERVICE", "ACCESS_BACKGROUND_LOCATION"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "<PERSON><PERSON><PERSON> que <PERSON> acceda a tu ubicación para mostrar motoristas cercanos y calcular rutas."}]]}}