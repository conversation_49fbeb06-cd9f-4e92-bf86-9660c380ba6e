import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ScrollView,
  TextInput,
} from 'react-native';
import { useApp } from '../context/AppContext';

const WalletScreen = ({ navigation }) => {
  const { balance, rechargeBalance, user } = useApp();
  const [customAmount, setCustomAmount] = useState('');
  const [loading, setLoading] = useState(false);

  const predefinedAmounts = [10, 19, 50, 100];

  const handleRecharge = async (amount) => {
    if (amount <= 0) {
      Alert.alert('Error', 'El monto debe ser mayor a $0');
      return;
    }

    setLoading(true);

    try {
      // Simular proceso de pago
      await new Promise(resolve => setTimeout(resolve, 2000));

      rechargeBalance(amount);
      
      Alert.alert(
        'Recarga exitosa',
        `Se han agregado $${amount.toFixed(2)} a tu cuenta`,
        [{ text: 'OK', onPress: () => setCustomAmount('') }]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo procesar la recarga. Intenta nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleCustomRecharge = () => {
    const amount = parseFloat(customAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Error', 'Ingresa un monto válido');
      return;
    }
    handleRecharge(amount);
  };

  const getBalanceStatus = () => {
    if (balance >= 100) {
      return {
        status: 'excellent',
        message: 'Saldo excelente',
        color: '#28a745',
        icon: '✅'
      };
    } else if (balance >= 50) {
      return {
        status: 'good',
        message: 'Saldo bueno',
        color: '#007AFF',
        icon: '👍'
      };
    } else if (balance >= 10) {
      return {
        status: 'low',
        message: 'Saldo bajo',
        color: '#ffc107',
        icon: '⚠️'
      };
    } else {
      return {
        status: 'critical',
        message: 'Saldo crítico - Recarga requerida',
        color: '#dc3545',
        icon: '🚨'
      };
    }
  };

  const balanceStatus = getBalanceStatus();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Atrás</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Billetera</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Balance Card */}
        <View style={styles.balanceCard}>
          <Text style={styles.balanceLabel}>Saldo actual</Text>
          <Text style={styles.balanceAmount}>${balance.toFixed(2)}</Text>
          
          <View style={[styles.statusContainer, { backgroundColor: balanceStatus.color + '20' }]}>
            <Text style={styles.statusIcon}>{balanceStatus.icon}</Text>
            <Text style={[styles.statusText, { color: balanceStatus.color }]}>
              {balanceStatus.message}
            </Text>
          </View>
        </View>

        {/* System Info */}
        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>💡 Sistema de Recarga Maclaren</Text>
          <View style={styles.infoItem}>
            <Text style={styles.infoText}>• Recarga inicial: $19</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoText}>• Al llegar a $100, se consumen $10 automáticamente</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoText}>• Cada viaje descuenta el 10% del valor</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoText}>• Saldo mínimo para motoristas: $10</Text>
          </View>
        </View>

        {/* Quick Recharge */}
        <View style={styles.rechargeSection}>
          <Text style={styles.sectionTitle}>Recarga rápida</Text>
          <View style={styles.amountGrid}>
            {predefinedAmounts.map((amount) => (
              <TouchableOpacity
                key={amount}
                style={[
                  styles.amountButton,
                  amount === 19 && styles.recommendedAmount
                ]}
                onPress={() => handleRecharge(amount)}
                disabled={loading}
              >
                <Text style={[
                  styles.amountText,
                  amount === 19 && styles.recommendedAmountText
                ]}>
                  ${amount}
                </Text>
                {amount === 19 && (
                  <Text style={styles.recommendedLabel}>Recomendado</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Custom Amount */}
        <View style={styles.customSection}>
          <Text style={styles.sectionTitle}>Monto personalizado</Text>
          <View style={styles.customInputContainer}>
            <TextInput
              style={styles.customInput}
              value={customAmount}
              onChangeText={setCustomAmount}
              placeholder="Ingresa el monto"
              keyboardType="numeric"
              placeholderTextColor="#999"
            />
            <TouchableOpacity
              style={[styles.customButton, loading && styles.disabledButton]}
              onPress={handleCustomRecharge}
              disabled={loading || !customAmount}
            >
              <Text style={styles.customButtonText}>Recargar</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Transaction History Placeholder */}
        <View style={styles.historySection}>
          <Text style={styles.sectionTitle}>Historial reciente</Text>
          <View style={styles.historyItem}>
            <View style={styles.historyIcon}>
              <Text>💳</Text>
            </View>
            <View style={styles.historyDetails}>
              <Text style={styles.historyTitle}>Recarga inicial</Text>
              <Text style={styles.historyDate}>Hoy</Text>
            </View>
            <Text style={styles.historyAmount}>+$0.00</Text>
          </View>
        </View>
      </ScrollView>

      {/* Loading Overlay */}
      {loading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Procesando recarga...</Text>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    fontSize: 16,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  placeholder: {
    width: 50,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  balanceCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginTop: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  balanceLabel: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  balanceAmount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  statusIcon: {
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#1a1a1a',
  },
  infoItem: {
    marginBottom: 6,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  rechargeSection: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  amountGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  amountButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '48%',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e1e5e9',
  },
  recommendedAmount: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  amountText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  recommendedAmountText: {
    color: '#007AFF',
  },
  recommendedLabel: {
    fontSize: 10,
    color: '#007AFF',
    marginTop: 4,
  },
  customSection: {
    marginTop: 24,
  },
  customInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customInput: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e1e5e9',
    marginRight: 12,
  },
  customButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 14,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  customButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  historySection: {
    marginTop: 24,
    marginBottom: 40,
  },
  historyItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  historyDetails: {
    flex: 1,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  historyDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  historyAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#28a745',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#1a1a1a',
  },
});

export default WalletScreen;
