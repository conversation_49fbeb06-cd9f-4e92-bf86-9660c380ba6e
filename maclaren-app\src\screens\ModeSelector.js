import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useApp } from '../context/AppContext';

const ModeSelector = ({ navigation }) => {
  const { user, userMode, switchMode, balance, canTakeRides } = useApp();

  const handleModeSelect = (mode) => {
    if (mode === 'driver' && !canTakeRides()) {
      Alert.alert(
        'Saldo insuficiente',
        'Debes recargar al menos $10 para poder recibir viajes como motorista.',
        [
          {
            text: 'Recargar',
            onPress: () => navigation.navigate('Wallet'),
          },
          {
            text: 'Cancelar',
            style: 'cancel',
          },
        ]
      );
      return;
    }

    switchMode(mode);
    
    if (mode === 'passenger') {
      navigation.navigate('PassengerScreen');
    } else {
      navigation.navigate('DriverScreen');
    }
  };

  const handleWalletPress = () => {
    navigation.navigate('Wallet');
  };

  const handleProfilePress = () => {
    navigation.navigate('Profile');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Text style={styles.greeting}>¡Hola, {user?.name}!</Text>
          <Text style={styles.subtitle}>¿Cómo quieres usar Maclaren hoy?</Text>
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.balanceButton} onPress={handleWalletPress}>
            <Text style={styles.balanceText}>${balance.toFixed(2)}</Text>
            <Text style={styles.balanceLabel}>Saldo</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.profileButton} onPress={handleProfilePress}>
            <Text style={styles.profileIcon}>👤</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Mode Selection */}
      <View style={styles.modeContainer}>
        {/* Passenger Mode */}
        <TouchableOpacity
          style={[
            styles.modeCard,
            userMode === 'passenger' && styles.activeModeCard
          ]}
          onPress={() => handleModeSelect('passenger')}
        >
          <View style={styles.modeIcon}>
            <Text style={styles.modeEmoji}>🚶‍♂️</Text>
          </View>
          <Text style={styles.modeTitle}>Pasajero</Text>
          <Text style={styles.modeDescription}>
            Solicita un viaje y llega a tu destino de forma segura
          </Text>
          <View style={styles.modeFeatures}>
            <Text style={styles.featureText}>• Solicitar viajes</Text>
            <Text style={styles.featureText}>• Ver motoristas disponibles</Text>
            <Text style={styles.featureText}>• Seguimiento en tiempo real</Text>
          </View>
        </TouchableOpacity>

        {/* Driver Mode */}
        <TouchableOpacity
          style={[
            styles.modeCard,
            userMode === 'driver' && styles.activeModeCard,
            !canTakeRides() && styles.disabledModeCard
          ]}
          onPress={() => handleModeSelect('driver')}
        >
          <View style={styles.modeIcon}>
            <Text style={styles.modeEmoji}>🏍️</Text>
          </View>
          <Text style={styles.modeTitle}>Motorista</Text>
          <Text style={styles.modeDescription}>
            Acepta viajes y genera ingresos conduciendo
          </Text>
          <View style={styles.modeFeatures}>
            <Text style={styles.featureText}>• Recibir solicitudes</Text>
            <Text style={styles.featureText}>• Ganar dinero</Text>
            <Text style={styles.featureText}>• Horarios flexibles</Text>
          </View>
          
          {!canTakeRides() && (
            <View style={styles.warningContainer}>
              <Text style={styles.warningText}>
                ⚠️ Saldo mínimo requerido: $10
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Current Mode Indicator */}
      <View style={styles.currentModeContainer}>
        <Text style={styles.currentModeText}>
          Modo actual: {userMode === 'passenger' ? 'Pasajero' : 'Motorista'}
        </Text>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity style={styles.quickActionButton} onPress={handleWalletPress}>
          <Text style={styles.quickActionEmoji}>💳</Text>
          <Text style={styles.quickActionText}>Billetera</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.quickActionButton} onPress={handleProfilePress}>
          <Text style={styles.quickActionEmoji}>⚙️</Text>
          <Text style={styles.quickActionText}>Perfil</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  userInfo: {
    flex: 1,
  },
  greeting: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 12,
    alignItems: 'center',
  },
  balanceText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  balanceLabel: {
    color: 'white',
    fontSize: 10,
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e1e5e9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileIcon: {
    fontSize: 20,
  },
  modeContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  modeCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  activeModeCard: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  disabledModeCard: {
    opacity: 0.6,
  },
  modeIcon: {
    alignItems: 'center',
    marginBottom: 12,
  },
  modeEmoji: {
    fontSize: 48,
  },
  modeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  modeDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  modeFeatures: {
    alignItems: 'flex-start',
  },
  featureText: {
    fontSize: 12,
    color: '#333',
    marginBottom: 4,
  },
  warningContainer: {
    backgroundColor: '#fff3cd',
    padding: 8,
    borderRadius: 8,
    marginTop: 12,
  },
  warningText: {
    fontSize: 12,
    color: '#856404',
    textAlign: 'center',
  },
  currentModeContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  currentModeText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 20,
    justifyContent: 'space-around',
  },
  quickActionButton: {
    alignItems: 'center',
    padding: 16,
  },
  quickActionEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  quickActionText: {
    fontSize: 12,
    color: '#666',
  },
});

export default ModeSelector;
