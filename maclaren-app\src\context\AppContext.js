import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Estados iniciales
const initialState = {
  user: null,
  userMode: 'passenger', // 'passenger' o 'driver'
  balance: 0,
  isAuthenticated: false,
  activeRides: [],
  rideRequests: [],
  location: null,
  loading: false,
};

// Tipos de acciones
const ActionTypes = {
  SET_USER: 'SET_USER',
  SET_USER_MODE: 'SET_USER_MODE',
  SET_BALANCE: 'SET_BALANCE',
  ADD_BALANCE: 'ADD_BALANCE',
  DEDUCT_BALANCE: 'DEDUCT_BALANCE',
  SET_AUTHENTICATED: 'SET_AUTHENTICATED',
  ADD_RIDE_REQUEST: 'ADD_RIDE_REQUEST',
  REMOVE_RIDE_REQUEST: 'REMOVE_RIDE_REQUEST',
  ADD_ACTIVE_RIDE: 'ADD_ACTIVE_RIDE',
  REMOVE_ACTIVE_RIDE: 'REMOVE_ACTIVE_RIDE',
  SET_LOCATION: 'SET_LOCATION',
  SET_LOADING: 'SET_LOADING',
  RESET_STATE: 'RESET_STATE',
};

// Reducer
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_USER:
      return { ...state, user: action.payload };
    
    case ActionTypes.SET_USER_MODE:
      return { ...state, userMode: action.payload };
    
    case ActionTypes.SET_BALANCE:
      return { ...state, balance: action.payload };
    
    case ActionTypes.ADD_BALANCE:
      return { ...state, balance: state.balance + action.payload };
    
    case ActionTypes.DEDUCT_BALANCE:
      const newBalance = Math.max(0, state.balance - action.payload);
      return { ...state, balance: newBalance };
    
    case ActionTypes.SET_AUTHENTICATED:
      return { ...state, isAuthenticated: action.payload };
    
    case ActionTypes.ADD_RIDE_REQUEST:
      return { 
        ...state, 
        rideRequests: [...state.rideRequests, action.payload] 
      };
    
    case ActionTypes.REMOVE_RIDE_REQUEST:
      return { 
        ...state, 
        rideRequests: state.rideRequests.filter(req => req.id !== action.payload) 
      };
    
    case ActionTypes.ADD_ACTIVE_RIDE:
      return { 
        ...state, 
        activeRides: [...state.activeRides, action.payload] 
      };
    
    case ActionTypes.REMOVE_ACTIVE_RIDE:
      return { 
        ...state, 
        activeRides: state.activeRides.filter(ride => ride.id !== action.payload) 
      };
    
    case ActionTypes.SET_LOCATION:
      return { ...state, location: action.payload };
    
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case ActionTypes.RESET_STATE:
      return initialState;
    
    default:
      return state;
  }
};

// Context
const AppContext = createContext();

// Provider
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Cargar datos persistidos al iniciar
  useEffect(() => {
    loadPersistedData();
  }, []);

  // Persistir datos importantes
  useEffect(() => {
    saveDataToStorage();
  }, [state.user, state.balance, state.userMode]);

  const loadPersistedData = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      const balance = await AsyncStorage.getItem('userBalance');
      const userMode = await AsyncStorage.getItem('userMode');

      if (userData) {
        const user = JSON.parse(userData);
        dispatch({ type: ActionTypes.SET_USER, payload: user });
        dispatch({ type: ActionTypes.SET_AUTHENTICATED, payload: true });
      }

      if (balance) {
        dispatch({ type: ActionTypes.SET_BALANCE, payload: parseFloat(balance) });
      }

      if (userMode) {
        dispatch({ type: ActionTypes.SET_USER_MODE, payload: userMode });
      }
    } catch (error) {
      console.error('Error loading persisted data:', error);
    }
  };

  const saveDataToStorage = async () => {
    try {
      if (state.user) {
        await AsyncStorage.setItem('userData', JSON.stringify(state.user));
      }
      await AsyncStorage.setItem('userBalance', state.balance.toString());
      await AsyncStorage.setItem('userMode', state.userMode);
    } catch (error) {
      console.error('Error saving data to storage:', error);
    }
  };

  // Funciones de utilidad
  const login = (userData) => {
    dispatch({ type: ActionTypes.SET_USER, payload: userData });
    dispatch({ type: ActionTypes.SET_AUTHENTICATED, payload: true });
  };

  const logout = async () => {
    try {
      await AsyncStorage.multiRemove(['userData', 'userBalance', 'userMode']);
      dispatch({ type: ActionTypes.RESET_STATE });
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const switchMode = (mode) => {
    dispatch({ type: ActionTypes.SET_USER_MODE, payload: mode });
  };

  const rechargeBalance = (amount) => {
    dispatch({ type: ActionTypes.ADD_BALANCE, payload: amount });
  };

  const deductBalance = (amount) => {
    dispatch({ type: ActionTypes.DEDUCT_BALANCE, payload: amount });
  };

  const canTakeRides = () => {
    return state.balance >= 10; // Mínimo $10 para tomar viajes
  };

  const calculateRideFee = (rideAmount) => {
    return rideAmount * 0.1; // 10% del valor del viaje
  };

  const processRidePayment = (rideAmount) => {
    const fee = calculateRideFee(rideAmount);
    dispatch({ type: ActionTypes.DEDUCT_BALANCE, payload: fee });
    
    // Si el balance llega a $100, se consumen $10
    if (state.balance >= 100) {
      dispatch({ type: ActionTypes.DEDUCT_BALANCE, payload: 10 });
    }
  };

  const value = {
    ...state,
    dispatch,
    login,
    logout,
    switchMode,
    rechargeBalance,
    deductBalance,
    canTakeRides,
    calculateRideFee,
    processRidePayment,
    ActionTypes,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Hook personalizado
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
