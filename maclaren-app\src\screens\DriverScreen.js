import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { useApp } from '../context/AppContext';

const DriverScreen = ({ navigation }) => {
  const { 
    user, 
    balance, 
    rideRequests, 
    dispatch, 
    ActionTypes, 
    canTakeRides,
    calculateRideFee,
    processRidePayment 
  } = useApp();
  
  const [refreshing, setRefreshing] = useState(false);
  const [acceptingRide, setAcceptingRide] = useState(null);

  const onRefresh = () => {
    setRefreshing(true);
    // Simular actualización de datos
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleAcceptRide = (rideRequest) => {
    if (!canTakeRides()) {
      Alert.alert(
        'Saldo insuficiente',
        'Necesitas al menos $10 de saldo para aceptar viajes. Ve a tu billetera para recargar.',
        [
          {
            text: 'Ir a billetera',
            onPress: () => navigation.navigate('Wallet'),
          },
          { text: 'Cancelar', style: 'cancel' },
        ]
      );
      return;
    }

    const fee = calculateRideFee(rideRequest.price);
    
    Alert.alert(
      'Aceptar viaje',
      `¿Aceptar este viaje?\n\nPrecio: $${rideRequest.price}\nComisión (10%): $${fee.toFixed(2)}\nTu ganancia: $${(rideRequest.price - fee).toFixed(2)}`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Aceptar',
          onPress: () => confirmAcceptRide(rideRequest),
        },
      ]
    );
  };

  const confirmAcceptRide = (rideRequest) => {
    setAcceptingRide(rideRequest.id);

    // Simular proceso de aceptación
    setTimeout(() => {
      // Procesar pago de comisión
      processRidePayment(rideRequest.price);

      // Crear viaje activo
      const activeRide = {
        ...rideRequest,
        driverId: user.id,
        driverName: user.name,
        status: 'accepted',
        acceptedAt: new Date().toISOString(),
      };

      // Agregar a viajes activos
      dispatch({
        type: ActionTypes.ADD_ACTIVE_RIDE,
        payload: activeRide,
      });

      // Remover de solicitudes
      dispatch({
        type: ActionTypes.REMOVE_RIDE_REQUEST,
        payload: rideRequest.id,
      });

      setAcceptingRide(null);

      Alert.alert(
        'Viaje aceptado',
        `Has aceptado el viaje. El pasajero ha sido notificado.\n\nSaldo actual: $${balance.toFixed(2)}`,
        [{ text: 'OK' }]
      );
    }, 2000);
  };

  const handleSwitchMode = () => {
    navigation.navigate('ModeSelector');
  };

  const handleWalletPress = () => {
    navigation.navigate('Wallet');
  };

  if (!canTakeRides()) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>¡Hola, {user?.name}!</Text>
            <Text style={styles.subtitle}>Modo: Motorista 🏍️</Text>
          </View>
          <TouchableOpacity style={styles.switchButton} onPress={handleSwitchMode}>
            <Text style={styles.switchButtonText}>Cambiar modo</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.blockedContainer}>
          <Text style={styles.blockedIcon}>🚨</Text>
          <Text style={styles.blockedTitle}>Saldo insuficiente</Text>
          <Text style={styles.blockedMessage}>
            Debes recargar al menos $10 para seguir recibiendo viajes
          </Text>
          <Text style={styles.currentBalance}>
            Saldo actual: ${balance.toFixed(2)}
          </Text>
          
          <TouchableOpacity style={styles.rechargeButton} onPress={handleWalletPress}>
            <Text style={styles.rechargeButtonText}>Recargar ahora</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>¡Hola, {user?.name}!</Text>
          <Text style={styles.subtitle}>Modo: Motorista 🏍️</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.balanceButton} onPress={handleWalletPress}>
            <Text style={styles.balanceText}>${balance.toFixed(2)}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.switchButton} onPress={handleSwitchMode}>
            <Text style={styles.switchButtonText}>Cambiar</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Status Card */}
      <View style={styles.statusCard}>
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Estado</Text>
          <Text style={styles.statusValue}>🟢 Disponible</Text>
        </View>
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Viajes hoy</Text>
          <Text style={styles.statusValue}>0</Text>
        </View>
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Ganancias</Text>
          <Text style={styles.statusValue}>$0.00</Text>
        </View>
      </View>

      {/* Ride Requests */}
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.requestsSection}>
          <Text style={styles.sectionTitle}>
            Solicitudes de viaje ({rideRequests.length})
          </Text>
          
          {rideRequests.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyIcon}>🔍</Text>
              <Text style={styles.emptyTitle}>No hay solicitudes</Text>
              <Text style={styles.emptyMessage}>
                Las nuevas solicitudes de viaje aparecerán aquí
              </Text>
            </View>
          ) : (
            rideRequests.map((request) => (
              <View key={request.id} style={styles.requestCard}>
                <View style={styles.requestHeader}>
                  <Text style={styles.passengerName}>👤 {request.passengerName}</Text>
                  <Text style={styles.requestPrice}>${request.price}</Text>
                </View>
                
                <View style={styles.requestRoute}>
                  <View style={styles.routeItem}>
                    <Text style={styles.routeLabel}>📍 Desde:</Text>
                    <Text style={styles.routeText}>{request.pickup}</Text>
                  </View>
                  <View style={styles.routeItem}>
                    <Text style={styles.routeLabel}>🎯 Hasta:</Text>
                    <Text style={styles.routeText}>{request.destination}</Text>
                  </View>
                </View>

                <View style={styles.requestFooter}>
                  <View style={styles.feeInfo}>
                    <Text style={styles.feeText}>
                      Comisión: ${calculateRideFee(request.price).toFixed(2)} (10%)
                    </Text>
                    <Text style={styles.earningsText}>
                      Tu ganancia: ${(request.price - calculateRideFee(request.price)).toFixed(2)}
                    </Text>
                  </View>
                  
                  <TouchableOpacity
                    style={[
                      styles.acceptButton,
                      acceptingRide === request.id && styles.acceptButtonLoading
                    ]}
                    onPress={() => handleAcceptRide(request)}
                    disabled={acceptingRide === request.id}
                  >
                    <Text style={styles.acceptButtonText}>
                      {acceptingRide === request.id ? 'Aceptando...' : 'Aceptar'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  greeting: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  balanceButton: {
    backgroundColor: '#28a745',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginRight: 8,
  },
  balanceText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  switchButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  switchButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  statusCard: {
    backgroundColor: 'white',
    flexDirection: 'row',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  statusItem: {
    flex: 1,
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  statusValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  requestsSection: {
    marginTop: 20,
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  emptyState: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  requestCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  requestPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#28a745',
  },
  requestRoute: {
    marginBottom: 16,
  },
  routeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  routeLabel: {
    fontSize: 14,
    color: '#666',
    width: 60,
  },
  routeText: {
    fontSize: 14,
    color: '#1a1a1a',
    flex: 1,
  },
  requestFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  feeInfo: {
    flex: 1,
  },
  feeText: {
    fontSize: 12,
    color: '#dc3545',
  },
  earningsText: {
    fontSize: 12,
    color: '#28a745',
    fontWeight: 'bold',
  },
  acceptButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  acceptButtonLoading: {
    backgroundColor: '#ccc',
  },
  acceptButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  blockedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  blockedIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  blockedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#dc3545',
    marginBottom: 12,
    textAlign: 'center',
  },
  blockedMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 16,
  },
  currentBalance: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 32,
  },
  rechargeButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  rechargeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default DriverScreen;
