# 🏎️ Maclaren - Aplicación de Transporte

Una aplicación móvil de transporte tipo Uber desarrollada con React Native y Expo, diseñada específicamente para motoristas y pasajeros.

## 🚀 Características Principales

### Sistema de Recarga Único
- **Recarga inicial**: $19 (recomendado)
- **Sistema automático**: Al llegar a $100, se consumen $10 automáticamente
- **Comisión por viaje**: 10% del valor de cada viaje
- **Saldo mínimo para motoristas**: $10 (sin saldo = bloqueo de viajes)

### Modos de Usuario
- **👤 Modo Pasajero**: Solicitar viajes y ver motoristas disponibles
- **🏍️ Modo Motorista**: Recibir y aceptar solicitudes de viaje
- **🔄 Cambio dinámico**: Alternar entre modos fácilmente

### Funcionalidades
- ✅ Autenticación simple
- 💳 Sistema de billetera integrado
- 🔍 Búsqueda de viajes en tiempo real
- ⭐ Perfiles verificados por seguridad
- 📱 Interfaz intuitiva y dinámica

## 🛠️ Instalación y Configuración

### Prerrequisitos
- Node.js (v16 o superior)
- npm o yarn
- Expo CLI
- Expo Go app en tu dispositivo móvil

### Pasos de Instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd maclaren-app
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Iniciar la aplicación**
   ```bash
   npm start
   ```

4. **Abrir en dispositivo**
   - Escanea el código QR con Expo Go (Android) o Cámara (iOS)
   - O presiona 'a' para Android, 'i' para iOS, 'w' para web

## 📱 Cómo Usar la Aplicación

### Primer Uso
1. **Registro**: Ingresa tu nombre y número de teléfono
2. **Verificación**: Tu perfil se marca como verificado automáticamente
3. **Selección de modo**: Elige entre Pasajero o Motorista

### Como Pasajero 👤
1. Selecciona "Modo Pasajero"
2. Completa el formulario de viaje:
   - 📍 Punto de recogida
   - 🎯 Destino
   - 💰 Precio ofrecido
3. Envía la solicitud
4. Espera a que un motorista acepte

### Como Motorista 🏍️
1. **Requisito**: Tener al menos $10 de saldo
2. Selecciona "Modo Motorista"
3. Ve las solicitudes de viaje disponibles
4. Revisa los detalles:
   - Información del pasajero
   - Ruta del viaje
   - Precio y comisión
5. Acepta el viaje que prefieras

### Sistema de Billetera 💳
1. Ve a "Billetera" desde cualquier pantalla
2. **Recarga rápida**: $10, $19 (recomendado), $50, $100
3. **Monto personalizado**: Ingresa cualquier cantidad
4. **Historial**: Ve tus transacciones recientes

## 🔧 Tecnologías Utilizadas

- **React Native**: Framework principal
- **Expo**: Plataforma de desarrollo
- **React Navigation**: Navegación entre pantallas
- **Context API**: Manejo de estado global
- **AsyncStorage**: Persistencia de datos local
- **Expo Location**: Servicios de geolocalización

## 📋 Estructura del Proyecto

```
maclaren-app/
├── src/
│   ├── components/          # Componentes reutilizables
│   ├── screens/            # Pantallas principales
│   │   ├── LoginScreen.js
│   │   ├── ModeSelector.js
│   │   ├── PassengerScreen.js
│   │   ├── DriverScreen.js
│   │   ├── WalletScreen.js
│   │   └── ProfileScreen.js
│   ├── context/            # Estado global
│   │   └── AppContext.js
│   ├── navigation/         # Configuración de navegación
│   │   └── AppNavigator.js
│   ├── services/           # Lógica de negocio
│   └── utils/              # Utilidades
├── assets/                 # Recursos (imágenes, iconos)
├── App.js                  # Componente principal
└── app.json               # Configuración de Expo
```

## 🎯 Flujo de la Aplicación

1. **Login** → Ingreso de datos básicos
2. **Mode Selector** → Selección Pasajero/Motorista
3. **Passenger/Driver Screen** → Funcionalidades específicas
4. **Wallet** → Gestión de saldo y recargas
5. **Profile** → Información personal y configuración

## 💡 Características del Sistema de Recarga

### Para Motoristas:
- Recarga $19 inicialmente
- Cada viaje descuenta 10% del precio
- Al llegar a $100, se descuentan $10 automáticamente
- Sin saldo mínimo ($10) = no pueden recibir viajes

### Para Pasajeros:
- No necesitan saldo para solicitar viajes
- Pueden cambiar a modo motorista cuando quieran

## 🚨 Mensajes del Sistema

- **Saldo crítico**: "Debes recargar $10 para seguir recibiendo viajes"
- **Recarga exitosa**: Confirmación con nuevo saldo
- **Viaje aceptado**: Notificación automática
- **Cambio de modo**: Confirmación de cambio

## 🔄 Estados de la Aplicación

- **No autenticado**: Pantalla de login
- **Autenticado**: Selector de modo y funcionalidades
- **Modo Pasajero**: Solicitar viajes
- **Modo Motorista**: Recibir viajes (con validación de saldo)

## 📞 Soporte

Para soporte técnico o consultas:
- **WhatsApp**: +1234567890
- **Email**: <EMAIL>

## 📄 Licencia

© 2024 Maclaren App - Todos los derechos reservados

---

**¡Disfruta usando Maclaren! 🏎️**
