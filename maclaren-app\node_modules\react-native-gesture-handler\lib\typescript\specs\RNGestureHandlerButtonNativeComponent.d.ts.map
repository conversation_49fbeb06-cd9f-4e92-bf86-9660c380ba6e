{"version": 3, "file": "RNGestureHandlerButtonNativeComponent.d.ts", "sourceRoot": "", "sources": ["../../../src/specs/RNGestureHandlerButtonNativeComponent.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,KAAK,EACL,WAAW,EACX,KAAK,EACN,MAAM,2CAA2C,CAAC;AACnD,OAAO,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1D,UAAU,WAAY,SAAQ,SAAS;IACrC,SAAS,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACvC,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,OAAO,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACrC,WAAW,CAAC,EAAE,UAAU,CAAC;IACzB,YAAY,CAAC,EAAE,KAAK,CAAC;IACrB,kBAAkB,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACjD,WAAW,CAAC,EAAE,KAAK,CAAC;IACpB,WAAW,CAAC,EAAE,UAAU,CAAC;IACzB,WAAW,CAAC,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CAC5C;;AAED,wBAA6E"}