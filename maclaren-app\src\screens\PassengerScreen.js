import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  TextInput,
  Alert,
  ScrollView,
} from 'react-native';
import { useApp } from '../context/AppContext';

const PassengerScreen = ({ navigation }) => {
  const { user, dispatch, ActionTypes } = useApp();
  const [pickup, setPickup] = useState('');
  const [destination, setDestination] = useState('');
  const [ridePrice, setRidePrice] = useState('');
  const [availableDrivers, setAvailableDrivers] = useState([]);
  const [requestingRide, setRequestingRide] = useState(false);
  const [activeRequest, setActiveRequest] = useState(null);

  // Simular motoristas disponibles
  useEffect(() => {
    const mockDrivers = [
      {
        id: '1',
        name: '<PERSON>',
        rating: 4.9,
        vehicle: 'Honda CBR 250',
        distance: '2 min',
        verified: true,
      },
      {
        id: '2',
        name: '<PERSON>',
        rating: 4.8,
        vehicle: 'Yamaha FZ',
        distance: '3 min',
        verified: true,
      },
      {
        id: '3',
        name: '<PERSON>',
        rating: 4.7,
        vehicle: 'Suzuki GN 125',
        distance: '5 min',
        verified: false,
      },
    ];
    setAvailableDrivers(mockDrivers);
  }, []);

  const handleRequestRide = () => {
    if (!pickup.trim() || !destination.trim() || !ridePrice.trim()) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }

    const price = parseFloat(ridePrice);
    if (isNaN(price) || price <= 0) {
      Alert.alert('Error', 'Ingresa un precio válido');
      return;
    }

    const rideRequest = {
      id: Date.now().toString(),
      passengerId: user.id,
      passengerName: user.name,
      pickup: pickup.trim(),
      destination: destination.trim(),
      price: price,
      status: 'pending',
      timestamp: new Date().toISOString(),
    };

    setActiveRequest(rideRequest);
    setRequestingRide(true);

    // Agregar solicitud al estado global
    dispatch({
      type: ActionTypes.ADD_RIDE_REQUEST,
      payload: rideRequest,
    });

    Alert.alert(
      'Solicitud enviada',
      'Tu solicitud de viaje ha sido enviada a los motoristas disponibles',
      [{ text: 'OK' }]
    );
  };

  const handleCancelRequest = () => {
    Alert.alert(
      'Cancelar viaje',
      '¿Estás seguro de que quieres cancelar la solicitud?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Sí, cancelar',
          style: 'destructive',
          onPress: () => {
            if (activeRequest) {
              dispatch({
                type: ActionTypes.REMOVE_RIDE_REQUEST,
                payload: activeRequest.id,
              });
            }
            setActiveRequest(null);
            setRequestingRide(false);
            setPickup('');
            setDestination('');
            setRidePrice('');
          },
        },
      ]
    );
  };

  const handleSwitchMode = () => {
    navigation.navigate('ModeSelector');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>¡Hola, {user?.name}!</Text>
          <Text style={styles.subtitle}>Modo: Pasajero 🚶‍♂️</Text>
        </View>
        <TouchableOpacity style={styles.switchButton} onPress={handleSwitchMode}>
          <Text style={styles.switchButtonText}>Cambiar modo</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {!requestingRide ? (
          <>
            {/* Request Form */}
            <View style={styles.formCard}>
              <Text style={styles.formTitle}>Solicitar viaje</Text>
              
              <View style={styles.inputContainer}>
                <Text style={styles.label}>📍 Punto de recogida</Text>
                <TextInput
                  style={styles.input}
                  value={pickup}
                  onChangeText={setPickup}
                  placeholder="¿Dónde te recogemos?"
                  placeholderTextColor="#999"
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>🎯 Destino</Text>
                <TextInput
                  style={styles.input}
                  value={destination}
                  onChangeText={setDestination}
                  placeholder="¿A dónde vas?"
                  placeholderTextColor="#999"
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>💰 Precio ofrecido</Text>
                <TextInput
                  style={styles.input}
                  value={ridePrice}
                  onChangeText={setRidePrice}
                  placeholder="Ej: 15.00"
                  keyboardType="numeric"
                  placeholderTextColor="#999"
                />
              </View>

              <TouchableOpacity
                style={styles.requestButton}
                onPress={handleRequestRide}
              >
                <Text style={styles.requestButtonText}>Solicitar viaje</Text>
              </TouchableOpacity>
            </View>

            {/* Available Drivers */}
            <View style={styles.driversSection}>
              <Text style={styles.sectionTitle}>Motoristas disponibles</Text>
              {availableDrivers.map((driver) => (
                <View key={driver.id} style={styles.driverCard}>
                  <View style={styles.driverInfo}>
                    <View style={styles.driverHeader}>
                      <Text style={styles.driverName}>{driver.name}</Text>
                      <View style={styles.verificationBadge}>
                        {driver.verified ? (
                          <Text style={styles.verifiedText}>✅ Verificado</Text>
                        ) : (
                          <Text style={styles.unverifiedText}>⚠️ No verificado</Text>
                        )}
                      </View>
                    </View>
                    <Text style={styles.driverVehicle}>🏍️ {driver.vehicle}</Text>
                    <View style={styles.driverStats}>
                      <Text style={styles.rating}>⭐ {driver.rating}</Text>
                      <Text style={styles.distance}>📍 {driver.distance}</Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </>
        ) : (
          /* Active Request */
          <View style={styles.activeRequestCard}>
            <Text style={styles.activeRequestTitle}>🔍 Buscando motorista...</Text>
            
            <View style={styles.requestDetails}>
              <View style={styles.requestItem}>
                <Text style={styles.requestLabel}>Desde:</Text>
                <Text style={styles.requestValue}>{activeRequest?.pickup}</Text>
              </View>
              <View style={styles.requestItem}>
                <Text style={styles.requestLabel}>Hasta:</Text>
                <Text style={styles.requestValue}>{activeRequest?.destination}</Text>
              </View>
              <View style={styles.requestItem}>
                <Text style={styles.requestLabel}>Precio:</Text>
                <Text style={styles.requestValue}>${activeRequest?.price}</Text>
              </View>
            </View>

            <View style={styles.waitingContainer}>
              <Text style={styles.waitingText}>
                Tu solicitud ha sido enviada a los motoristas cercanos.
                Te notificaremos cuando alguien acepte tu viaje.
              </Text>
            </View>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancelRequest}
            >
              <Text style={styles.cancelButtonText}>Cancelar solicitud</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  greeting: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  switchButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  switchButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  formCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
  },
  requestButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  requestButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  driversSection: {
    marginTop: 24,
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  driverCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  driverInfo: {
    flex: 1,
  },
  driverHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  driverName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  verificationBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  verifiedText: {
    fontSize: 10,
    color: '#28a745',
  },
  unverifiedText: {
    fontSize: 10,
    color: '#ffc107',
  },
  driverVehicle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  driverStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rating: {
    fontSize: 14,
    color: '#333',
  },
  distance: {
    fontSize: 14,
    color: '#666',
  },
  activeRequestCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginTop: 20,
    alignItems: 'center',
  },
  activeRequestTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 24,
  },
  requestDetails: {
    width: '100%',
    marginBottom: 24,
  },
  requestItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  requestLabel: {
    fontSize: 14,
    color: '#666',
  },
  requestValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  waitingContainer: {
    backgroundColor: '#f0f8ff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  waitingText: {
    fontSize: 14,
    color: '#007AFF',
    textAlign: 'center',
    lineHeight: 20,
  },
  cancelButton: {
    backgroundColor: '#dc3545',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default PassengerScreen;
