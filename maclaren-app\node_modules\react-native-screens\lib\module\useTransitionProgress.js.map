{"version": 3, "names": ["React", "TransitionProgressContext", "useTransitionProgress", "progress", "useContext", "undefined", "Error"], "sourceRoot": "../../src", "sources": ["useTransitionProgress.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,yBAAyB,MAAM,6BAA6B;AAEnE,eAAe,SAASC,qBAAqBA,CAAA,EAAG;EAC9C,MAAMC,QAAQ,GAAGH,KAAK,CAACI,UAAU,CAACH,yBAAyB,CAAC;EAE5D,IAAIE,QAAQ,KAAKE,SAAS,EAAE;IAC1B,MAAM,IAAIC,KAAK,CACb,wFACF,CAAC;EACH;EAEA,OAAOH,QAAQ;AACjB", "ignoreList": []}