import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { useApp } from '../context/AppContext';

// Screens
import LoginScreen from '../screens/LoginScreen';
import ModeSelector from '../screens/ModeSelector';
import PassengerScreen from '../screens/PassengerScreen';
import DriverScreen from '../screens/DriverScreen';
import WalletScreen from '../screens/WalletScreen';
import ProfileScreen from '../screens/ProfileScreen';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { isAuthenticated } = useApp();

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: { backgroundColor: '#f8f9fa' },
        }}
      >
        {!isAuthenticated ? (
          // Auth Stack
          <Stack.Screen 
            name="Login" 
            component={LoginScreen}
            options={{
              animationTypeForReplace: 'push',
            }}
          />
        ) : (
          // Main App Stack
          <>
            <Stack.Screen 
              name="ModeSelector" 
              component={ModeSelector}
              options={{
                gestureEnabled: false,
              }}
            />
            <Stack.Screen 
              name="PassengerScreen" 
              component={PassengerScreen}
            />
            <Stack.Screen 
              name="DriverScreen" 
              component={DriverScreen}
            />
            <Stack.Screen 
              name="Wallet" 
              component={WalletScreen}
            />
            <Stack.Screen 
              name="Profile" 
              component={ProfileScreen}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
