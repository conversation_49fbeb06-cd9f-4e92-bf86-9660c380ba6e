# 🧪 Guía de Pruebas - Maclaren App

## Escenarios de Prueba

### 1. <PERSON>tro y Login ✅
**Pasos:**
1. Abrir la aplicación
2. Ingresar nombre: "<PERSON>"
3. Ingresar teléfono: "12345678"
4. Presionar "Iniciar Sesión"

**Resultado esperado:**
- Login exitoso
- Redirección a ModeSelector
- Perfil marcado como verificado

### 2. Sistema de Recarga 💳
**Escenario A: Recarga inicial**
1. Ir a Billetera
2. Seleccionar $19 (recomendado)
3. Confirmar recarga

**Resultado esperado:**
- Saldo: $19.00
- Mensaje de confirmación
- Estado: "Saldo bajo"

**Escenario B: Recarga personalizada**
1. Ingresar monto: $50
2. Presionar "Recargar"

**Resultado esperado:**
- Saldo: $69.00
- <PERSON><PERSON><PERSON>: "Saldo bueno"

### 3. Modo <PERSON> 👤
**Pasos:**
1. <PERSON><PERSON><PERSON><PERSON><PERSON> "Modo Pasajero"
2. Completar formulario:
   - <PERSON><PERSON>: "Centro Comercial"
   - Has<PERSON>: "Universidad"
   - <PERSON><PERSON>: "15"
3. Presionar "Solicitar viaje"

**Resultado esperado:**
- Solicitud enviada
- Pantalla de espera
- Motoristas pueden ver la solicitud

### 4. Modo Motorista 🏍️
**Escenario A: Con saldo suficiente**
1. Tener al menos $10 de saldo
2. Seleccionar "Modo Motorista"
3. Ver solicitudes disponibles
4. Aceptar un viaje

**Resultado esperado:**
- Lista de solicitudes visible
- Cálculo de comisión correcto (10%)
- Descuento automático del saldo

**Escenario B: Sin saldo suficiente**
1. Tener menos de $10 de saldo
2. Intentar seleccionar "Modo Motorista"

**Resultado esperado:**
- Pantalla de bloqueo
- Mensaje: "Debes recargar $10 para seguir recibiendo viajes"
- Botón para ir a billetera

### 5. Sistema de Comisiones 💰
**Prueba del 10%:**
1. Como motorista, aceptar viaje de $20
2. Verificar descuento: $2.00 (10%)
3. Ganancia neta: $18.00

**Prueba del descuento automático:**
1. Recargar hasta llegar a $100
2. Verificar descuento automático de $10
3. Saldo final: $90

### 6. Cambio de Modo 🔄
**Pasos:**
1. Estar en modo Pasajero
2. Presionar "Cambiar modo"
3. Seleccionar "Motorista"

**Resultado esperado:**
- Cambio exitoso
- Persistencia del modo seleccionado
- Funcionalidades correctas según el modo

### 7. Persistencia de Datos 💾
**Pasos:**
1. Realizar login
2. Hacer una recarga
3. Cerrar y reabrir la aplicación

**Resultado esperado:**
- Usuario sigue logueado
- Saldo se mantiene
- Modo seleccionado se conserva

## Casos Edge 🚨

### Saldo Exacto en $10
- Motorista con exactamente $10
- Debe poder recibir viajes
- Al aceptar un viaje, puede quedar bloqueado

### Recarga de $100
- Al llegar exactamente a $100
- Descuento automático de $10
- Saldo final: $90

### Solicitudes Múltiples
- Varios pasajeros solicitando viajes
- Motoristas ven todas las solicitudes
- Solo uno puede aceptar cada viaje

## Validaciones de UI 🎨

### Responsive Design
- Funciona en diferentes tamaños de pantalla
- Elementos bien alineados
- Texto legible

### Estados de Loading
- Indicadores durante procesos
- Botones deshabilitados cuando corresponde
- Mensajes informativos

### Navegación
- Botones de "Atrás" funcionan
- Navegación fluida entre pantallas
- No hay pantallas bloqueadas

## Pruebas de Rendimiento ⚡

### Tiempo de Carga
- Login: < 2 segundos
- Cambio de pantalla: < 1 segundo
- Recarga: < 3 segundos

### Memoria
- No memory leaks
- Navegación fluida
- App no se cierra inesperadamente

## Checklist Final ✅

- [ ] Login funciona correctamente
- [ ] Sistema de recarga operativo
- [ ] Modo pasajero completo
- [ ] Modo motorista con validaciones
- [ ] Cálculo de comisiones correcto
- [ ] Persistencia de datos
- [ ] Navegación fluida
- [ ] UI responsive
- [ ] Mensajes informativos
- [ ] Estados de error manejados

## Bugs Conocidos 🐛

*Ninguno reportado hasta el momento*

## Mejoras Futuras 🚀

1. **Mapas en tiempo real**
2. **Chat entre usuario y motorista**
3. **Historial de viajes detallado**
4. **Sistema de calificaciones**
5. **Notificaciones push**
6. **Métodos de pago adicionales**

---

**Nota**: Esta aplicación es una versión MVP (Producto Mínimo Viable) con las funcionalidades core implementadas.
